from odoo import api, fields, models, _


class DataMaskingStrategy(models.Model):
    _name = 'th.data.masking.strategy'
    _description = '<PERSON><PERSON> thuật mask Dữ liệu'
    _order = 'name'

    name = fields.Char(string='Tên', required=True)
    th_code = fields.Char(string='Mã', required=True)
    description = fields.Text(string='Mô tả')
    th_strategy_type = fields.Selection([
        ('fixed', 'Giá trị Cố định'),
        ('random', 'Giá trị Ngẫu nhiên'),
        ('pattern', 'Mẫu'),
        ('nullify', 'Đặt về Null'),
        ('hash', 'Gi<PERSON> trị Hash'),
        ('partial', 'Mask Một phần'),
        ('custom', 'Hàm Tùy chỉnh')
    ], string='Loại Chiến lược', required=True, default='fixed')
    th_fixed_value = fields.Char(string='Giá trị Cố định')
    th_pattern = fields.Char(string='Mẫu', help="Ví dụ: 'XXXX-XXXX-####' trong đó X được thay thế bằng ký tự ngẫu nhiên và # bằng số ngẫu nhiên")
    th_partial_keep_start = fields.Integer(string='Giữ Ký tự Đầu', default=0)
    th_partial_keep_end = fields.Integer(string='Giữ Ký tự Cuối', default=0)
    th_custom_python_code = fields.Text(string='Mã Python Tùy chỉnh')
    active = fields.Boolean(default=True)
    
    th_applicable_field_types = fields.Char(
        string='Loại Trường Áp dụng',
        help="Danh sách phân cách bằng dấu phẩy các loại trường mà chiến lược này có thể áp dụng (char,text,integer,v.v.)"
    )

    def _mask_value(self, value, field_type):
        """
        mask giá trị theo chiến lược
        :param value: giá trị gốc
        :param field_type: loại trường (char, text, integer, v.v.)
        :return: giá trị đã mask
        """
        if value is None or value is False:
            return value

        if self.th_strategy_type == 'nullify':
            return False
        elif self.th_strategy_type == 'fixed':
            return self.th_fixed_value
        elif self.th_strategy_type == 'random':
            return self._generate_random_value(field_type)
        elif self.th_strategy_type == 'pattern':
            return self._apply_pattern(value)
        elif self.th_strategy_type == 'hash':
            return self._hash_value(value)
        elif self.th_strategy_type == 'partial':
            return self._partial_mask(value)
        elif self.th_strategy_type == 'custom':
            return self._apply_custom_masking(value, field_type)
        
        return value

    def _generate_random_value(self, field_type):
        import random
        import string
        from datetime import datetime, timedelta
        
        if field_type in ('char', 'text', 'html'):
            length = random.randint(5, 10)
            return ''.join(random.choices(string.ascii_letters + string.digits, k=length))
        elif field_type in ('integer', 'float', 'monetary'):
            return random.randint(1, 1000)
        elif field_type == 'date':
            delta = timedelta(days=random.randint(0, 365))
            return (datetime.now() - delta).date()
        elif field_type == 'datetime':
            delta = timedelta(days=random.randint(0, 365))
            return datetime.now() - delta
        elif field_type == 'boolean':
            return random.choice([True, False])
        return False

    def _apply_pattern(self, value):
        import random
        import string
        
        if not self.th_pattern or not isinstance(value, str):
            return value
            
        result = ''
        for char in self.th_pattern:
            if char == 'X':
                result += random.choice(string.ascii_letters)
            elif char == '#':
                result += random.choice(string.digits)
            else:
                result += char
        return result

    def _hash_value(self, value):
        import hashlib
        if isinstance(value, str):
            return hashlib.md5(value.encode()).hexdigest()
        return hashlib.md5(str(value).encode()).hexdigest()

    def _partial_mask(self, value):
        if not isinstance(value, str):
            return value
            
        if len(value) <= (self.th_partial_keep_start + self.th_partial_keep_end):
            return value
            
        visible_start = value[:self.th_partial_keep_start] if self.th_partial_keep_start > 0 else ''
        visible_end = value[-self.th_partial_keep_end:] if self.th_partial_keep_end > 0 else ''
        masked_length = len(value) - len(visible_start) - len(visible_end)
        masked_part = '*' * masked_length
        
        return visible_start + masked_part + visible_end

    def _apply_custom_masking(self, value, field_type):
        if not self.th_custom_python_code:
            return value
            
        try:
            locals_dict = {
                'value': value, 
                'field_type': field_type,
                'result': value  # Kết quả mặc định là giá trị gốc
            }
            exec(self.th_custom_python_code, globals(), locals_dict)
            return locals_dict.get('result', value)
        except Exception as e:
            self.env['ir.logging'].create({
                'name': 'data_masking',
                'type': 'server',
                'dbname': self.env.cr.dbname,
                'level': 'ERROR',
                'message': f"Lỗi trong mã mask tùy chỉnh: {str(e)}",
            })
            return value
        
    def _map_field_type_to_postgres(self, field_type):
        """
        Chuyển đổi loại trường từ Odoo sang loại trường PostgreSQL
        :param field_type: loại trường trong Odoo
        :return: loại trường tương ứng trong PostgreSQL
        """
        mapping = {
            'character varying': 'char',
            'text': 'text',
            'integer': 'integer',
            'float': 'float',
            'date': 'date',
            'datetime': 'timestamp',
            'boolean': 'boolean',
        }
        
        return mapping.get(field_type, 'text')

    def get_sql_update_statement(self, table_name, field_name, field_type):
        """
        Tạo câu lệnh SQL để cập nhật trường với giá trị đã mask
        
        :param table_name: tên bảng cơ sở dữ liệu
        :param field_name: trường cần mask
        :param field_type: loại trường PostgreSQL
        :return: câu lệnh SQL UPDATE
        """
        field_map = self._map_field_type_to_postgres(field_type)
        if self.th_strategy_type == 'nullify':
            return f'UPDATE "{table_name}" SET "{field_name}" = NULL;'
        
        elif self.th_strategy_type == 'fixed':
            # Xử lý các loại trường khác nhau cho giá trị cố định
            if field_map in ('varchar', 'text', 'char'):
                value = f"'{self.th_fixed_value or ''}'"
            elif field_map in ('int', 'integer', 'bigint', 'numeric', 'float'):
                value = self.th_fixed_value if self.th_fixed_value and self.th_fixed_value.replace('.','').replace('-','').isdigit() else '0'
            elif field_map in ('date', 'timestamp'):
                value = f"'{self.th_fixed_value}'" if self.th_fixed_value else 'NULL'
            elif field_map == 'boolean':
                value = 'FALSE'
            else:
                value = 'NULL'
                
            return f'UPDATE "{table_name}" SET "{field_name}" = {value};'
            
        elif self.th_strategy_type == 'random':
            # Tạo giá trị ngẫu nhiên sử dụng hàm PostgreSQL
            if field_map in ('varchar', 'text', 'char'):
                sql = f'''
                UPDATE "{table_name}" SET "{field_name}" = (
                    SELECT string_agg(
                        chr(65 + floor(random() * 26)::integer), ''
                    )
                    FROM generate_series(1, 8)
                );
                '''
                return sql
            elif field_map in ('int', 'integer', 'bigint'):
                return f'UPDATE "{table_name}" SET "{field_name}" = floor(random() * 1000)::integer;'
            elif field_map in ('numeric', 'float'):
                return f'UPDATE "{table_name}" SET "{field_name}" = random() * 1000;'
            elif field_map == 'date':
                return f'UPDATE "{table_name}" SET "{field_name}" = CURRENT_DATE - (random() * 365)::integer;'
            elif field_map == 'timestamp':
                return f'UPDATE "{table_name}" SET "{field_name}" = CURRENT_TIMESTAMP - interval \'1 day\' * (random() * 365);'
            elif field_map == 'boolean':
                return f'UPDATE "{table_name}" SET "{field_name}" = (random() > 0.5);'
                
        elif self.th_strategy_type == 'pattern':
            # Tạo mask dựa trên mẫu sử dụng hàm PostgreSQL
            if field_map in ('varchar', 'text', 'char') and self.th_pattern:
                pattern = self.th_pattern or 'XXX'
                sql = f'''
                UPDATE "{table_name}" SET "{field_name}" = (
                    SELECT string_agg(
                        CASE 
                            WHEN substr('{pattern}', generate_series, 1) = 'X' THEN 
                                chr(65 + floor(random() * 26)::integer)
                            WHEN substr('{pattern}', generate_series, 1) = 'x' THEN 
                                chr(97 + floor(random() * 26)::integer)
                            WHEN substr('{pattern}', generate_series, 1) = '#' THEN 
                                chr(48 + floor(random() * 10)::integer)
                            ELSE substr('{pattern}', generate_series, 1)
                        END, ''
                    )
                    FROM generate_series(1, length('{pattern}'))
                );
                '''
                return sql
                
        elif self.th_strategy_type == 'hash':
            # Hash giá trị hiện tại
            if field_map in ('varchar', 'text', 'char'):
                return f'UPDATE "{table_name}" SET "{field_name}" = md5("{field_name}"::text);'
            else:
                return f'UPDATE "{table_name}" SET "{field_name}" = md5("{field_name}"::text);'
                
        elif self.th_strategy_type == 'partial':
            # mask một phần - giữ ký tự đầu và cuối
            if field_map in ('varchar', 'text', 'char'):
                start_chars = self.th_partial_keep_start or 0
                end_chars = self.th_partial_keep_end or 0
                if start_chars == 0 and end_chars == 0:
                    return f'UPDATE "{table_name}" SET "{field_name}" = regexp_replace("{field_name}", \'.\', \'*\', \'g\');'
                elif end_chars == 0:
                    return f'''UPDATE "{table_name}" SET "{field_name}" = 
                        CASE 
                            WHEN length("{field_name}") <= {start_chars} THEN "{field_name}"
                            ELSE left("{field_name}", {start_chars}) || repeat('*', length("{field_name}") - {start_chars})
                        END;'''
                elif start_chars == 0:
                    return f'''UPDATE "{table_name}" SET "{field_name}" = 
                        CASE 
                            WHEN length("{field_name}") <= {end_chars} THEN "{field_name}"
                            ELSE repeat('*', length("{field_name}") - {end_chars}) || right("{field_name}", {end_chars})
                        END;'''
                else:
                    return f'''UPDATE "{table_name}" SET "{field_name}" = 
                        CASE 
                            WHEN length("{field_name}") <= {start_chars + end_chars} THEN "{field_name}"
                            ELSE left("{field_name}", {start_chars}) || 
                                 repeat('*', length("{field_name}") - {start_chars} - {end_chars}) || 
                                 right("{field_name}", {end_chars})
                        END;'''
                        
        # Giải pháp dự phòng mặc định
        return f'UPDATE "{table_name}" SET "{field_name}" = NULL;'
    
    def get_sql_unique_update_statement(self, table_name, field_name, field_type):
        """
        Tạo câu lệnh SQL để cập nhật trường với giá trị duy nhất đã mask
        
        :param table_name: tên bảng cơ sở dữ liệu
        :param field_name: tên trường cơ sở dữ liệu
        :param field_type: loại trường cơ sở dữ liệu
        :return: câu lệnh SQL UPDATE với giá trị duy nhất
        """
        field_map = self._map_field_type_to_postgres(field_type)
        
        if self.th_strategy_type == 'nullify':
            return f'UPDATE "{table_name}" SET "{field_name}" = NULL;'
        
        elif self.th_strategy_type == 'fixed':
            # Với giá trị cố định, ta sẽ thêm row_number để đảm bảo unique
            if field_map in ('varchar', 'text', 'char'):
                fixed_val = self.th_fixed_value or 'DATA'
                return f'''
                UPDATE "{table_name}" SET "{field_name}" = '{fixed_val}' || '_' || 
                    (SELECT row_number() OVER (ORDER BY id) FROM "{table_name}" t2 WHERE t2.id = "{table_name}".id);
                '''
            elif field_map in ('int', 'integer', 'bigint', 'numeric', 'float'):
                return f'''
                UPDATE "{table_name}" SET "{field_name}" = 
                    (SELECT row_number() OVER (ORDER BY id) FROM "{table_name}" t2 WHERE t2.id = "{table_name}".id);
                '''
            elif field_map == 'boolean':
                return f'UPDATE "{table_name}" SET "{field_name}" = (id % 2 = 0);'
            else:
                return f'UPDATE "{table_name}" SET "{field_name}" = NULL;'
                
        elif self.th_strategy_type == 'random':
            # Tạo giá trị ngẫu nhiên unique sử dụng row_number và random seed
            if field_map in ('varchar', 'text', 'char'):
                return f'''
                UPDATE "{table_name}" SET "{field_name}" = (
                    SELECT 'MASK' || '_' || 
                           (row_number() OVER (ORDER BY random())) || '_' ||
                           string_agg(
                               chr(65 + floor(random() * 26)::integer), ''
                           )
                    FROM generate_series(1, 4)
                );
                '''
            elif field_map in ('int', 'integer', 'bigint'):
                return f'''
                UPDATE "{table_name}" SET "{field_name}" = 
                    1000000 + (SELECT row_number() OVER (ORDER BY id) FROM "{table_name}" t2 WHERE t2.id = "{table_name}".id);
                '''
            elif field_map in ('numeric', 'float'):
                return f'''
                UPDATE "{table_name}" SET "{field_name}" = 
                    1000000.0 + (SELECT row_number() OVER (ORDER BY id) FROM "{table_name}" t2 WHERE t2.id = "{table_name}".id);
                '''
            elif field_map == 'date':
                return f'''
                UPDATE "{table_name}" SET "{field_name}" = 
                    DATE '2000-01-01' + (SELECT row_number() OVER (ORDER BY id) FROM "{table_name}" t2 WHERE t2.id = "{table_name}".id);
                '''
            elif field_map == 'timestamp':
                return f'''
                UPDATE "{table_name}" SET "{field_name}" = 
                    TIMESTAMP '2000-01-01 00:00:00' + 
                    INTERVAL '1 day' * (SELECT row_number() OVER (ORDER BY id) FROM "{table_name}" t2 WHERE t2.id = "{table_name}".id);
                '''
            elif field_map == 'boolean':
                return f'UPDATE "{table_name}" SET "{field_name}" = (id % 2 = 0);'
                
        elif self.th_strategy_type == 'pattern':
            # Tạo mask unique dựa trên mẫu với row_number
            if field_map in ('varchar', 'text', 'char') and self.th_pattern:
                pattern = self.th_pattern or 'XXX'
                return f'''
                UPDATE "{table_name}" SET "{field_name}" = (
                    SELECT string_agg(
                        CASE 
                            WHEN substr('{pattern}', generate_series, 1) = 'X' THEN 
                                chr(65 + floor(random() * 26)::integer)
                            WHEN substr('{pattern}', generate_series, 1) = 'x' THEN 
                                chr(97 + floor(random() * 26)::integer)
                            WHEN substr('{pattern}', generate_series, 1) = '#' THEN 
                                chr(48 + floor(random() * 10)::integer)
                            ELSE substr('{pattern}', generate_series, 1)
                        END, ''
                    ) || '_' || (SELECT row_number() OVER (ORDER BY id) FROM "{table_name}" t2 WHERE t2.id = "{table_name}".id)
                    FROM generate_series(1, length('{pattern}'))
                );
                '''
                
        elif self.th_strategy_type == 'hash':
            # Hash giá trị với row_number để đảm bảo unique
            if field_map in ('varchar', 'text', 'char'):
                return f'''
                UPDATE "{table_name}" SET "{field_name}" = 
                    md5(concat("{field_name}"::text, '_', id::text));
                '''
            else:
                return f'''
                UPDATE "{table_name}" SET "{field_name}" = 
                    md5(concat("{field_name}"::text, '_', id::text));
                '''
                
        elif self.th_strategy_type == 'partial':
            # mask một phần với row_number để đảm bảo unique
            if field_map in ('varchar', 'text', 'char'):
                start_chars = self.th_partial_keep_start or 0
                end_chars = self.th_partial_keep_end or 0
                row_suffix = f''' || '_' || (SELECT row_number() OVER (ORDER BY id) FROM "{table_name}" t2 WHERE t2.id = "{table_name}".id)'''
                
                if start_chars == 0 and end_chars == 0:
                    return f'UPDATE "{table_name}" SET "{field_name}" = regexp_replace("{field_name}", \'.\', \'*\', \'g\') {row_suffix};'
                elif end_chars == 0:
                    return f'''UPDATE "{table_name}" SET "{field_name}" = 
                        CASE 
                            WHEN length("{field_name}") <= {start_chars} THEN "{field_name}" {row_suffix}
                            ELSE left("{field_name}", {start_chars}) || repeat('*', length("{field_name}") - {start_chars}) {row_suffix}
                        END;'''
                elif start_chars == 0:
                    return f'''UPDATE "{table_name}" SET "{field_name}" = 
                        CASE 
                            WHEN length("{field_name}") <= {end_chars} THEN "{field_name}" {row_suffix}
                            ELSE repeat('*', length("{field_name}") - {end_chars}) || right("{field_name}", {end_chars}) {row_suffix}
                        END;'''
                else:
                    return f'''UPDATE "{table_name}" SET "{field_name}" = 
                        CASE 
                            WHEN length("{field_name}") <= {start_chars + end_chars} THEN "{field_name}" {row_suffix}
                            ELSE left("{field_name}", {start_chars}) || 
                                 repeat('*', length("{field_name}") - {start_chars} - {end_chars}) || 
                                 right("{field_name}", {end_chars}) {row_suffix}
                        END;'''
        
        elif self.th_strategy_type == 'custom':
            # Với custom code, ta không thể đảm bảo unique trong SQL
            # Nên fallback về giá trị với row_number
            return f'''
            UPDATE "{table_name}" SET "{field_name}" = 
                'CUSTOM_' || (SELECT row_number() OVER (ORDER BY id) FROM "{table_name}" t2 WHERE t2.id = "{table_name}".id);
            '''
                        
        # Giải pháp dự phòng mặc định với unique ID
        return f'''
        UPDATE "{table_name}" SET "{field_name}" = 
            'MASKED_' || (SELECT row_number() OVER (ORDER BY id) FROM "{table_name}" t2 WHERE t2.id = "{table_name}".id);
        '''