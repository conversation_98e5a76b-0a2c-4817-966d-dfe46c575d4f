import os
import tempfile
import logging
import time
import base64
import threading
import subprocess
import shutil
import json
import odoo
import odoo.sql_db
import odoo.modules.registry
from datetime import datetime, timedelta
from odoo import api, fields, models, tools, _
from odoo.exceptions import UserError
from odoo.tools import config
from odoo.service import db
from odoo.tools import find_pg_tool, exec_pg_environ

_logger = logging.getLogger(__name__)


class MaskedBackupWizard(models.TransientModel):
    _name = 'th.masked.backup.wizard'
    _description = 'Tạo Sao lưu Cơ sở Dữ liệu đã mask'

    th_backup_format = fields.Selection([
        ('zip', 'ZIP (bao gồm filestore)'),
        ('dump', 'Chỉ SQL')
    ], string='Định dạng', default='zip', required=True)
    
    th_include_all_masking_rules = fields.Boolean(
        string='Áp dụng Tất cả Quy tắc mask', 
        default=True,
        help='Áp dụng tất cả quy tắc mask đang hoạt động cho sao lưu cơ sở dữ liệu'
    )
    
    th_masking_rule_ids = fields.Many2many(
        'th.data.masking.rule',
        string='Quy tắc mask cần Áp dụng',
        domain=[('active', '=', True), ('th_apply_to_backups', '=', True)]
    )
    
    th_temp_db_name = fields.Char(string='Tên Cơ sở Dữ liệu Tạm thời', compute='_compute_temp_db_name')
    
    th_run_in_background = fields.Boolean(
        string='Chạy trong nền', 
        default=True,
        help='Chạy quá trình backup trong nền để không block giao diện người dùng'
    )
    
    @api.depends('th_backup_format')
    def _compute_temp_db_name(self):
        """Tạo tên cơ sở dữ liệu tạm thời duy nhất"""
        timestamp = datetime.now().strftime('%Y%m%d%H%M%S')
        for rec in self:
            rec.th_temp_db_name = f"temp_mask_{self.env.cr.dbname}_{timestamp}"
    
    def action_create_masked_backup(self):
        """Tạo sao lưu cơ sở dữ liệu đã mask bằng cách sử dụng lại logic backup của base"""
        self.ensure_one()
        
        # Xác thực quy tắc mask
        masking_rules = self.th_masking_rule_ids
        if self.th_include_all_masking_rules:
            masking_rules = self.env['th.data.masking.rule'].search([
                ('active', '=', True),
                ('th_apply_to_backups', '=', True)
            ])
            
        if not masking_rules:
            raise UserError(_("Không có quy tắc mask nào được chọn. Vui lòng chọn ít nhất một quy tắc mask."))
            
        # Tạo bản ghi sao lưu
        backup_record = self.env['th.database.masked.backup'].create({
            'th_backup_format': self.th_backup_format,
            'th_status': 'running',
            'th_masked_field_count': len(masking_rules),
            'th_masked_rules_ids': [(6, 0, masking_rules.ids)],
            'th_filename': f"masked_{self.env.cr.dbname}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.{self.th_backup_format}",
        })
        
        # Chạy backup process
        if self.th_run_in_background:
            # Commit bản ghi backup trước khi chạy trong background
            self.env.cr.commit()
            
            # Chạy ngay lập tức
            self._run_backup_process(backup_record.id, masking_rules.ids, self.th_temp_db_name, self.th_backup_format)
            # Chạy trong background thread
            # thread = threading.Thread(
            #     target=self._run_backup_process,
            #     args=(backup_record.id, masking_rules.ids, self.th_temp_db_name, self.th_backup_format)
            # )
            # thread.start()
            
            # Trả về thông báo cho người dùng
            return {
                'type': 'ir.actions.act_window',
                'name': _('Sao lưu đã mask'),
                'res_model': 'th.database.masked.backup',
                'res_id': backup_record.id,
                'view_mode': 'form',
                'target': 'current',
            }
        else:
            # Commit bản ghi backup trước khi chạy
            self.env.cr.commit()
            
            # Chạy ngay lập tức
            self._run_backup_process(backup_record.id, masking_rules.ids, self.th_temp_db_name, self.th_backup_format)
            
            # Trả về hành động để xem bản ghi sao lưu
            return {
                'type': 'ir.actions.act_window',
                'name': _('Sao lưu đã mask'),
                'res_model': 'th.database.masked.backup',
                'res_id': backup_record.id,
                'view_mode': 'form',
                'target': 'current',
            }
    
    @api.model
    def _run_backup_process(self, backup_id, masking_rule_ids, temp_db_name, backup_format):
        """
        Thực thi toàn bộ quá trình sao lưu với mask sử dụng logic base của Odoo
        
        Workflow:
        1. Tạo temp database sử dụng PostgreSQL tools
        2. Áp dụng masking rules vào temp database
        3. Tạo backup file sử dụng dump_db() từ base Odoo 
        4. Dọn dẹp temp database
        
        Logic backup base được sử dụng:
        - pg_dump + pg_restore: Tạo copy database
        - dump_db(): Tạo backup file (ZIP hoặc SQL dump)
        - dump_db_manifest(): Tạo manifest file 
        """
        db_name = self.env.cr.dbname
        start_time = time.time()
        
        try:
            # Tạo registry và environment mới cho database hiện tại
            registry = odoo.registry(db_name)
            with registry.cursor() as new_cr:
                new_env = odoo.api.Environment(new_cr, self.env.uid, self.env.context)
                
                # Lấy bản ghi sao lưu với cursor mới
                backup_record = new_env['th.database.masked.backup'].browse(backup_id)
                if not backup_record.exists():
                    _logger.error(f"Không tìm thấy bản ghi sao lưu {backup_id}")
                    return
                    
                backup_record.add_to_log("Bắt đầu quá trình sao lưu đã mask với logic base của Odoo ")
                new_cr.commit()
                    
                # Tạo cơ sở dữ liệu tạm thời từ cơ sở dữ liệu hiện tại
                backup_record.add_to_log(f"Đang tạo cơ sở dữ liệu tạm thời '{temp_db_name}' ")
                new_cr.commit()
                
                # Tạo temp database - sử dụng static method call
                try:
                    self._create_temp_database_static(db_name, temp_db_name)
                    backup_record.add_to_log(f"Đã tạo thành công cơ sở dữ liệu tạm thời '{temp_db_name}' ")
                    new_cr.commit()
                except Exception as e:
                    backup_record.add_to_log(f"Lỗi tạo temp database: {e} ")
                    backup_record.write({
                        'th_status': 'failed',
                        'th_duration': (time.time() - start_time) / 60,
                    })
                    new_cr.commit()
                    return
        
                # Áp dụng quy tắc mask vào cơ sở dữ liệu tạm thời
                try:
                    masking_rules = new_env['th.data.masking.rule'].browse(masking_rule_ids)
                    backup_record.add_to_log(f"Đang áp dụng {len(masking_rules)} quy tắc mask ")
                    new_cr.commit()
                    
                    # Kết nối đến cơ sở dữ liệu tạm thời để áp dụng mask
                    temp_db_conn = self._connect_to_database_static(temp_db_name)
                    backup_record.add_to_log("Đã kết nối đến cơ sở dữ liệu tạm thời ")
                    new_cr.commit()
                    
                    # Xử lý quy tắc mask theo từng batch để tránh vấn đề bộ nhớ
                    for i, rule in enumerate(masking_rules, 1):
                        # Bước 1: Áp dụng SQL-based masking trước
                        sql_statements = rule.get_sql_mask_statements()
                        if sql_statements:
                            for sql in sql_statements:
                                try:
                                    with temp_db_conn.cursor() as cursor:
                                        cursor.execute(sql)
                                        temp_db_conn.commit()
                                except Exception as e:
                                    temp_db_conn.rollback()
                                    backup_record.add_to_log(
                                        f"Lỗi SQL mask {rule.model_id.name}: {e} "
                                    )
                                    new_cr.commit()

                        # Bước 2: Áp dụng custom masking cho các rule có custom strategy
                        custom_rules = rule.get_sql_mask_statements()
                        if custom_rules:
                            try:
                                masked_count = rule.apply_custom_masking_to_temp_db(temp_db_conn)
                                backup_record.add_to_log(
                                    f"({i}/{len(masking_rules)}) Custom mask: {masked_count} records trong {rule.model_id.name} "
                                )
                                new_cr.commit()
                            except Exception as e:
                                backup_record.add_to_log(
                                    f"Lỗi custom mask {rule.model_id.name}: {e} "
                                )
                                new_cr.commit()

                        # Log tổng kết cho rule này
                        total_fields = len(rule.th_data_masking_rule_line_ids)
                        sql_fields = total_fields - len(custom_rules)
                        backup_record.add_to_log(
                            f"({i}/{len(masking_rules)}) Hoàn thành mask {rule.model_id.name}: {sql_fields} SQL fields, {len(custom_rules)} custom fields "
                        )
                        new_cr.commit()
                    
                    # Đóng kết nối temp_db trước khi tạo backup
                    temp_db_conn.close()
                    backup_record.add_to_log("Hoàn thành quá trình mask dữ liệu ")
                    new_cr.commit()
                    
                except Exception as e:
                    backup_record.add_to_log(f"Lỗi trong quá trình mask: {e} ")
                    backup_record.write({
                        'th_status': 'failed',
                        'th_duration': (time.time() - start_time) / 60,
                    })
                    new_cr.commit()
                    self._cleanup_temp_database_static(temp_db_name)
                    return
                    
                #Tạo backup sử dụng logic base của Odoo
                try:
                    backup_record.add_to_log("Bắt đầu tạo file backup sử dụng logic base của Odoo ")
                    new_cr.commit()
                    backup_path = self._create_masked_backup_static(temp_db_name, backup_record.th_filename, backup_format)
                    backup_record.add_to_log(f"Backup file đã được tạo: {backup_path} ")
                    
                    # Cập nhật thông tin backup record
                    if os.path.exists(backup_path):
                        file_size = os.path.getsize(backup_path) // 1024  # Size in KB
                        backup_record.write({
                            'th_backup_path': backup_path,
                            'th_file_size': file_size,
                        })
                        new_cr.commit()
                    else:
                        raise Exception(f"Backup file không được tạo tại: {backup_path} ")
                    
                except Exception as e:
                    backup_record.add_to_log(f"Lỗi tạo backup file: {e} ")
                    backup_record.write({
                        'th_status': 'failed',
                        'th_duration': (time.time() - start_time) / 60,
                    })
                    new_cr.commit()
                    self._cleanup_temp_database_static(temp_db_name)
                    return
                    
                #Dọn dẹp cơ sở dữ liệu tạm thời
                try:
                    self._cleanup_temp_database_static(temp_db_name)
                    backup_record.add_to_log(f"Đã xóa cơ sở dữ liệu tạm thời '{temp_db_name}' ")
                    new_cr.commit()
                except Exception as e:
                    backup_record.add_to_log(f"Lỗi xóa cơ sở dữ liệu tạm thời: {e} ")
                    new_cr.commit()
                    
                # Hoàn thành backup
                duration = (time.time() - start_time) / 60  # in minutes
                backup_record.write({
                    'th_status': 'done',
                    'th_duration': duration,
                })
                backup_record.add_to_log(f"Quá trình backup hoàn tất trong {duration:.2f} phút ")
                new_cr.commit()
        
        except Exception as e:
            _logger.error(f"Lỗi trong quá trình backup: {e} ", exc_info=True)
            try:
                registry = odoo.registry(db_name)
                with registry.cursor() as error_cr:
                    error_env = odoo.api.Environment(error_cr, self.env.uid, self.env.context)
                    backup_record = error_env['th.database.masked.backup'].browse(backup_id)
                    if backup_record.exists():
                        backup_record.write({
                            'th_status': 'failed',
                            'th_duration': (time.time() - start_time) / 60,
                        })
                        backup_record.add_to_log(f"Lỗi trong quá trình backup: {e} ")
                        error_cr.commit()
                self._cleanup_temp_database_static(temp_db_name)
            except:
                pass
    
    @staticmethod
    def _connect_to_database_static(dbname):
        """Kết nối đến PostgreSQL database - static method"""
        import psycopg2
        from psycopg2.extensions import ISOLATION_LEVEL_AUTOCOMMIT
        
        db_params = {
            'dbname': dbname,
            'user': tools.config.get('db_user'),
            'password': tools.config.get('db_password'),
            'host': tools.config.get('db_host') or 'localhost',
            'port': tools.config.get('db_port') or 5432,
        }
        
        # Lọc bỏ các parameter None
        db_params = {k: v for k, v in db_params.items() if v is not None}
        
        try:
            conn = psycopg2.connect(**db_params)
            conn.set_isolation_level(ISOLATION_LEVEL_AUTOCOMMIT)
            return conn
        except Exception as e:
            _logger.error(f"Failed to connect to database {dbname}: {e}")
            raise

    @staticmethod
    def _create_masked_backup_static(db_name, filename, backup_format):
        """Tạo backup file sử dụng logic base của Odoo - static method"""
        # Tạo backup directory nếu chưa tồn tại
        backup_dir = os.path.join(tools.config['data_dir'], 'masked_backups')
        os.makedirs(backup_dir, exist_ok=True)
        
        # Tạo đường dẫn backup
        backup_path = os.path.join(backup_dir, filename)
        
        try:
            # Sử dụng trực tiếp hàm dump_db của base Odoo
            _logger.info('MASKED DUMP DB: %s format %s', db_name, backup_format)
            
            with open(backup_path, 'wb') as backup_stream:
                # Gọi trực tiếp hàm dump_db từ odoo.service.db
                from odoo.service.db import dump_db
                dump_db(db_name, backup_stream, backup_format)
                
        except Exception as e:
            _logger.error(f"Error creating masked backup using base logic: {e}")
            # Fallback to custom implementation nếu base logic fail
            return MaskedBackupWizard._create_masked_backup_fallback_static(db_name, filename, backup_format)
        
        return backup_path

    @staticmethod  
    def _create_masked_backup_fallback_static(db_name, filename, backup_format):
        """Fallback implementation - static method"""
        # Tạo backup directory nếu chưa tồn tại
        backup_dir = os.path.join(tools.config['data_dir'], 'masked_backups')
        os.makedirs(backup_dir, exist_ok=True)
        
        # Tạo đường dẫn backup
        backup_path = os.path.join(backup_dir, filename)
        
        _logger.info('MASKED DUMP DB (FALLBACK): %s format %s', db_name, backup_format)
        
        cmd = [find_pg_tool('pg_dump'), '--no-owner', db_name]
        env = exec_pg_environ()
        
        if backup_format == 'zip':
            with tempfile.TemporaryDirectory() as dump_dir:
                                
                # Tạo manifest từ temp database đã được mask
                with open(os.path.join(dump_dir, 'manifest.json'), 'w') as fh:
                    # Tạo manifest đơn giản cho masked backup
                    manifest = {
                        'odoo_dump': '1',
                        'db_name': db_name,
                        'version': odoo.release.version,
                        'version_info': odoo.release.version_info,
                        'major_version': odoo.release.major_version,
                        'modules': {}, 
                        'masked': True,
                        'masking_timestamp': datetime.now().isoformat(),
                    }
                    json.dump(manifest, fh, indent=4)
                
                # Dump SQL từ temp database đã được mask
                cmd.insert(-1, '--file=' + os.path.join(dump_dir, 'dump.sql'))
                subprocess.run(cmd, env=env, stdout=subprocess.DEVNULL, stderr=subprocess.STDOUT, check=True)
                
                # Tạo zip file
                with open(backup_path, 'wb') as zip_stream:
                    tools.osutil.zip_dir(dump_dir, zip_stream, include_dir=False, 
                                       fnct_sort=lambda file_name: file_name != 'dump.sql')
        else:
            # Format dump (PostgreSQL custom format)
            cmd.insert(-1, '--format=c')
            with open(backup_path, 'wb') as dump_file:
                stdout = subprocess.Popen(cmd, env=env, stdin=subprocess.DEVNULL, stdout=subprocess.PIPE).stdout
                shutil.copyfileobj(stdout, dump_file)
                stdout.close()
        
        return backup_path
    
    def _dump_db_manifest(self, cr):
        """Tạo manifest cho database backup sử dụng logic base của Odoo"""
        try:
            # Sử dụng trực tiếp hàm dump_db_manifest từ base Odoo
            from odoo.service.db import dump_db_manifest
            manifest = dump_db_manifest(cr)
            # Thêm thông tin đánh dấu đây là backup đã được mask
            manifest['masked'] = True
            manifest['masking_timestamp'] = datetime.now().isoformat()
            return manifest
        except Exception as e:
            # Fallback implementation nếu base logic không work
            pg_version = "%d.%d" % divmod(cr._obj.connection.server_version / 100, 100)
            cr.execute("SELECT name, latest_version FROM ir_module_module WHERE state = 'installed'")
            modules = dict(cr.fetchall())
            manifest = {
                'odoo_dump': '1',
                'db_name': cr.dbname,
                'version': odoo.release.version,
                'version_info': odoo.release.version_info,
                'major_version': odoo.release.major_version,
                'pg_version': pg_version,
                'modules': modules,
                'masked': True,  # Đánh dấu đây là backup đã được mask
                'masking_timestamp': datetime.now().isoformat(),
            }
            return manifest
        
    def _connect_to_database(self, dbname):
        """Kết nối đến PostgreSQL database"""
        import psycopg2
        from psycopg2.extensions import ISOLATION_LEVEL_AUTOCOMMIT
        
        db_params = {
            'dbname': dbname,
            'user': tools.config['db_user'],
            'password': tools.config['db_password'],
            'host': tools.config['db_host'],
            'port': tools.config['db_port'],
        }
        
        # Lọc bỏ các parameter None hoặc False
        db_params = {k: v for k, v in db_params.items() if v}
        
        try:
            conn = psycopg2.connect(**db_params)
            conn.set_isolation_level(ISOLATION_LEVEL_AUTOCOMMIT)
            return conn
        except Exception as e:
            _logger.error(f"Lỗi kết nối tới cơ sở dữ liệu {dbname}: {e}")
            raise
        
    def _cleanup_temp_database(self, dbname):
        """Dọn dẹp cơ sở dữ liệu tạm thời sử dụng logic base của Odoo"""
        try:
            # Sử dụng trực tiếp hàm exp_drop từ base Odoo
            from odoo.service.db import exp_drop
            exp_drop(dbname)
            _logger.info(f"Xóa bỏ cơ sở dữ liệu {dbname} thành công")
        except Exception as e:
            _logger.error(f"Lỗi xóa cơ sở dữ liệu {dbname}: {e}")
            # Fallback method nếu exp_drop không work
            try:
                # Cleanup registry and close connections
                odoo.modules.registry.Registry.delete(dbname)
                odoo.sql_db.close_db(dbname)
                
                # Connect to postgres db and drop the database
                postgres_db = odoo.sql_db.db_connect('postgres')
                with postgres_db.cursor() as cr:
                    cr._cnx.autocommit = True
                    # Terminate connections
                    pid_col = 'pid' if cr._cnx.server_version >= 90200 else 'procpid'
                    cr.execute(f"""SELECT pg_terminate_backend({pid_col})
                                  FROM pg_stat_activity
                                  WHERE datname = %s AND
                                        {pid_col} != pg_backend_pid()""", (dbname,))
                    # Drop database
                    cr.execute(f'DROP DATABASE IF EXISTS "{dbname}"')
                _logger.info(f"Successfully dropped database {dbname} using fallback method")
            except Exception as e2:
                _logger.error(f"Fallback cleanup also failed for {dbname}: {e2}")
                raise
    
    def _create_temp_database(self, source_db, target_db):
        """Tạo database tạm thời từ source database sử dụng logic base của Odoo"""
        try:
            # Sử dụng trực tiếp exp_duplicate_database từ base Odoo
            from odoo.service.db import exp_duplicate_database
            
            # Gọi trong context an toàn, không sử dụng self.env vì đang trong thread
            exp_duplicate_database(source_db, target_db)
            _logger.info(f"Thành công tạo cơ sở dữ liệu tạm thời {target_db} từ {source_db} ")
            
        except Exception as e:
            _logger.error(f"Lỗi tạo cơ sở dữ liệu tạm thời: {e}")
            # Fallback implementation nếu base function không work
            try:
                # Tạo empty database trước
                self._create_empty_database_direct(target_db)
                
                # Dump và restore để tạo copy
                with tempfile.NamedTemporaryFile() as temp_dump:
                    # Dump source database
                    from odoo.service.db import dump_db
                    with open(temp_dump.name, 'wb') as dump_file:
                        dump_db(source_db, dump_file, 'zip')
                    
                    # Restore to target database  
                    from odoo.service.db import restore_db
                    restore_db(target_db, temp_dump.name)
                    
                _logger.info(f"Cơ sở dữ liệu được tạo thành công {target_db} từ {source_db} ")
                
            except Exception as e2:
                _logger.error(f"Tạo cơ sở dữ liệu tạm thời thất bại: {e2}")
                raise

    def _create_empty_database_direct(self, db_name):
        """Tạo empty database trực tiếp không qua Odoo context"""
        try:
            # Kết nối trực tiếp đến PostgreSQL
            import psycopg2
            from psycopg2 import sql
            
            # Lấy thông tin kết nối từ config
            db_params = {
                'dbname': 'postgres',  # Kết nối đến postgres db để tạo db mới
                'user': tools.config.get('db_user') or 'odoo',
                'password': tools.config.get('db_password') or '',
                'host': tools.config.get('db_host') or 'localhost',
                'port': tools.config.get('db_port') or 5432,
            }
            
            # Lọc bỏ các parameter None hoặc False
            db_params = {k: v for k, v in db_params.items() if v}
            
            conn = psycopg2.connect(**db_params)
            conn.set_isolation_level(psycopg2.extensions.ISOLATION_LEVEL_AUTOCOMMIT)
            
            with conn.cursor() as cr:
                # Tạo database mới
                cr.execute(sql.SQL('CREATE DATABASE {}').format(sql.Identifier(db_name)))
                
            conn.close()
            _logger.info(f"Đã tạo thành công cơ sở dữ liệu trống {db_name}")
            
        except Exception as e:
            _logger.error(f"Lỗi tạo cơ sở dữ liệu trống {db_name}: {e}")
            raise
    
    @staticmethod
    def _create_temp_database_static(source_db, target_db):
        """Tạo database tạm thời - static method để tránh vấn đề connection"""
        try:
            # Method 1: Sử dụng PostgreSQL command line tools trực tiếp
            # Dump source database
            dump_cmd = [
                find_pg_tool('pg_dump'),
                '--no-owner',
                '--no-privileges', 
                '--format=custom',
                source_db
            ]
            
            # Tạo empty target database trước
            MaskedBackupWizard._create_empty_database_static(target_db)
            
            # Restore vào target database
            restore_cmd = [
                find_pg_tool('pg_restore'),
                '--no-owner',
                '--no-privileges',
                '--dbname=' + target_db
            ]
            
            env = exec_pg_environ()
            
            # Pipe dump output trực tiếp vào restore
            dump_process = subprocess.Popen(dump_cmd, env=env, stdout=subprocess.PIPE, stderr=subprocess.PIPE)
            restore_process = subprocess.Popen(restore_cmd, env=env, stdin=dump_process.stdout, stderr=subprocess.PIPE)
            
            dump_process.stdout.close()  # Đóng stdout của dump process để nhận SIGPIPE nếu restore process exit
            restore_output, restore_error = restore_process.communicate()
            dump_process.wait()
            
            if restore_process.returncode != 0 and restore_error:
                _logger.warning(f"Khôi phục cơ sở dữ liệu tạm thời {target_db}: {restore_error.decode()}")
            
        except Exception as e:
            # Cleanup nếu có lỗi
            try:
                MaskedBackupWizard._cleanup_temp_database_static(target_db)
            except:
                pass
            raise

    @staticmethod  
    def _create_empty_database_static(db_name):
        """Tạo empty database - static method"""
        try:
            import psycopg2
            from psycopg2 import sql
            
            # Lấy connection parameters từ config
            db_params = {
                'dbname': 'postgres',
                'user': tools.config.get('db_user'),
                'password': tools.config.get('db_password'),  
                'host': tools.config.get('db_host') or 'localhost',
                'port': tools.config.get('db_port') or 5432,
            }
            
            # Remove None values
            db_params = {k: v for k, v in db_params.items() if v is not None}
            
            conn = psycopg2.connect(**db_params)
            conn.set_isolation_level(psycopg2.extensions.ISOLATION_LEVEL_AUTOCOMMIT)
            
            with conn.cursor() as cr:
                # Terminate existing connections to the database if any
                cr.execute("""
                    SELECT pg_terminate_backend(pid)
                    FROM pg_stat_activity 
                    WHERE datname = %s AND pid != pg_backend_pid()
                """, (db_name,))
                
                # Drop database if exists
                cr.execute(sql.SQL('DROP DATABASE IF EXISTS {}').format(sql.Identifier(db_name)))
                
                # Create new database
                cr.execute(sql.SQL('CREATE DATABASE {}').format(sql.Identifier(db_name)))
                
            conn.close()
        except Exception as e:
            raise

    @staticmethod
    def _cleanup_temp_database_static(dbname):
        """Static method để cleanup temp database"""
        try:
            import psycopg2
            from psycopg2 import sql
            
            db_params = {
                'dbname': 'postgres',
                'user': tools.config.get('db_user'),
                'password': tools.config.get('db_password'),
                'host': tools.config.get('db_host') or 'localhost', 
                'port': tools.config.get('db_port') or 5432,
            }
            
            db_params = {k: v for k, v in db_params.items() if v is not None}
            
            conn = psycopg2.connect(**db_params)
            conn.set_isolation_level(psycopg2.extensions.ISOLATION_LEVEL_AUTOCOMMIT)
            
            with conn.cursor() as cr:
                # Terminate connections
                cr.execute("""
                    SELECT pg_terminate_backend(pid)
                    FROM pg_stat_activity
                    WHERE datname = %s AND pid != pg_backend_pid()
                """, (dbname,))
                
                # Drop database
                cr.execute(sql.SQL('DROP DATABASE IF EXISTS {}').format(sql.Identifier(dbname)))
                
            conn.close()
            
        except Exception as e:
            raise